"""
Enhanced DOM tools with layered architecture.

This module implements the layered DOM element extraction approach:
- Foundation Layer: Basic element extraction
- Classification Layer: Element type classification  
- Functional Layer: User intent-based classification

All functions maintain the existing return format using DOMFormatter.
"""

import logging
from typing import List, Optional
from langchain_core.tools import tool

from ..core.browser import get_global_browser
from ..dom.core.extractor import DOMExtractor
from ..dom.formatters.element_formatters import DOMFormatter

logger = logging.getLogger(__name__)


def _get_dom_extractor() -> DOMExtractor:
    """Get DOM extractor instance."""
    browser = get_global_browser()
    if not browser._is_initialized:
        browser._initialize()
    return DOMExtractor(browser)


# =============================================================================
# FOUNDATION LAYER - Basic element extraction
# =============================================================================


# =============================================================================
# CLASSIFICATION LAYER - By HTML tag type
# =============================================================================

@tool
def dom_get_buttons_enhanced(
        include_disabled: bool = False,
        include_hidden: bool = False,
        require_text: bool = True
) -> str:
    """
    Get all button elements with enhanced filtering options.
    
    Includes:
    - <button> tags
    - <input type="button|submit|reset">
    - role="button" elements
    
    Args:
        include_disabled: Whether to include disabled buttons
        include_hidden: Whether to include hidden buttons
        require_text: Whether to require text content or identifying attributes
        
    Returns:
        A formatted string listing all button elements.
    """
    try:
        extractor = _get_dom_extractor()
        all_elements = extractor.extract_all_elements()

        buttons = []
        for elem in all_elements:
            # Visibility filter
            if not include_hidden and not elem.is_visible:
                continue

            tag_name = elem.tag_name.lower()
            is_button = False

            # Standard button elements
            if tag_name == 'button':
                is_button = True
            elif tag_name == 'input' and elem.attributes.get('type', '').lower() in ['button', 'submit', 'reset']:
                is_button = True
            elif elem.attributes.get('role') == 'button':
                is_button = True

            if is_button:
                # Disabled filter
                is_disabled = elem.attributes.get('disabled') is not None
                if not include_disabled and is_disabled:
                    continue

                # Text requirement filter
                if require_text:
                    has_text = bool(elem.text_content and elem.text_content.strip())
                    has_value = bool(elem.attributes.get('value', '').strip())
                    has_title = bool(elem.attributes.get('title', '').strip())
                    has_aria_label = bool(elem.attributes.get('aria-label', '').strip())

                    if not (has_text or has_value or has_title or has_aria_label):
                        continue

                buttons.append(elem)

        result = DOMFormatter.format_buttons(buttons)
        logger.info(f"DOM get_buttons_enhanced: Found {len(buttons)} buttons")
        return result

    except Exception as e:
        error_msg = f"Failed to get buttons: {str(e)}"
        logger.error(f"DOM get_buttons_enhanced error: {error_msg}")
        return f"❌ {error_msg}"


@tool
def dom_get_inputs_enhanced(
        input_types: Optional[List[str]] = None,
        include_disabled: bool = False,
        include_readonly: bool = True
) -> str:
    """
    Get all input elements with enhanced filtering options.
    
    Includes:
    - <input> various types
    - <textarea>
    - <select>
    - contenteditable elements
    
    Args:
        input_types: List of input types to include (e.g., ['text', 'email', 'password'])
        include_disabled: Whether to include disabled inputs
        include_readonly: Whether to include readonly inputs
        
    Returns:
        A formatted string listing all input elements.
    """
    try:
        extractor = _get_dom_extractor()
        all_elements = extractor.extract_all_elements()

        inputs = []
        for elem in all_elements:
            if not elem.is_visible:
                continue

            tag_name = elem.tag_name.lower()
            is_input = False

            # Standard input elements
            if tag_name in ['input', 'textarea', 'select']:
                is_input = True
            elif elem.attributes.get('contenteditable') == 'true':
                is_input = True
            elif elem.attributes.get('role') in ['textbox', 'combobox', 'searchbox']:
                is_input = True

            if is_input:
                # Type filter
                if input_types and tag_name == 'input':
                    input_type = elem.attributes.get('type', 'text').lower()
                    if input_type not in input_types:
                        continue

                # Disabled filter
                if not include_disabled and elem.attributes.get('disabled') is not None:
                    continue

                # Readonly filter
                if not include_readonly and elem.attributes.get('readonly') is not None:
                    continue

                inputs.append(elem)

        result = DOMFormatter.format_inputs(inputs)
        logger.info(f"DOM get_inputs_enhanced: Found {len(inputs)} inputs")
        return result

    except Exception as e:
        error_msg = f"Failed to get inputs: {str(e)}"
        logger.error(f"DOM get_inputs_enhanced error: {error_msg}")
        return f"❌ {error_msg}"


@tool
def dom_get_links_enhanced(
        require_href: bool = True,
        include_anchors: bool = False,
        external_only: bool = False
) -> str:
    """
    Get all link elements with enhanced filtering options.
    
    Includes:
    - <a> tags
    - role="link" elements
    
    Args:
        require_href: Whether to require href attribute
        include_anchors: Whether to include page anchors (#section)
        external_only: Whether to include only external links
        
    Returns:
        A formatted string listing all link elements.
    """
    try:
        extractor = _get_dom_extractor()
        all_elements = extractor.extract_all_elements()

        links = []
        for elem in all_elements:
            if not elem.is_visible:
                continue

            tag_name = elem.tag_name.lower()
            is_link = False

            if tag_name == 'a':
                is_link = True
            elif elem.attributes.get('role') == 'link':
                is_link = True
            if is_link:
                href = elem.attributes.get('href', '')

                # Href requirement filter
                if require_href and not href:
                    continue

                # Anchor filter
                if not include_anchors and href.startswith('#'):
                    continue

                # External link filter
                if external_only:
                    if not href or href.startswith('#') or href.startswith('/') or 'javascript:' in href:
                        continue

                links.append(elem)

        result = DOMFormatter.format_links(links)
        logger.info(f"DOM get_links_enhanced: Found {len(links)} links")
        return result

    except Exception as e:
        error_msg = f"Failed to get links: {str(e)}"
        logger.error(f"DOM get_links_enhanced error: {error_msg}")
        return f"❌ {error_msg}"


# =============================================================================
# CLASSIFICATION LAYER - By functional role
# =============================================================================

@tool
def dom_get_form_elements() -> str:
    """
    Get all form-related elements from the current page.

    Includes:
    - Form containers
    - Form controls (inputs, selects, textareas)
    - Form labels
    - Form buttons

    Returns:
        A formatted string listing all form elements.
    """
    try:
        extractor = _get_dom_extractor()
        all_elements = extractor.extract_all_elements()

        form_elements = []
        for elem in all_elements:
            if not elem.is_visible:
                continue

            tag_name = elem.tag_name.lower()
            is_form_element = False

            # Form containers and controls
            if tag_name in ['form', 'fieldset', 'legend']:
                is_form_element = True
            elif tag_name in ['input', 'textarea', 'select', 'button', 'label']:
                is_form_element = True
            elif elem.attributes.get('role') in ['form', 'group']:
                is_form_element = True

            if is_form_element:
                form_elements.append(elem)

        result = DOMFormatter.format_form_elements(form_elements)
        logger.info(f"DOM get_form_elements: Found {len(form_elements)} form elements")
        return result

    except Exception as e:
        error_msg = f"Failed to get form elements: {str(e)}"
        logger.error(f"DOM get_form_elements error: {error_msg}")
        return f"❌ {error_msg}"


@tool
def dom_get_navigation_elements() -> str:
    """
    Get all navigation-related elements from the current page.

    Includes:
    - <nav> tags
    - role="navigation" elements
    - Menu-related elements
    - Breadcrumb navigation

    Returns:
        A formatted string listing all navigation elements.
    """
    try:
        extractor = _get_dom_extractor()
        all_elements = extractor.extract_all_elements()

        nav_elements = []
        for elem in all_elements:
            if not elem.is_visible:
                continue

            tag_name = elem.tag_name.lower()
            is_nav_element = False

            # Navigation tags
            if tag_name == 'nav':
                is_nav_element = True
            elif elem.attributes.get('role') in ['navigation', 'menubar', 'menu', 'menuitem']:
                is_nav_element = True
            else:
                # Check for navigation classes
                classes = elem.attributes.get('class', '').lower()
                if any(nav_class in classes for nav_class in ['nav', 'menu', 'navigation', 'breadcrumb']):
                    is_nav_element = True

            if is_nav_element:
                nav_elements.append(elem)

        result = DOMFormatter.format_navigation_elements(nav_elements)
        logger.info(f"DOM get_navigation_elements: Found {len(nav_elements)} navigation elements")
        return result

    except Exception as e:
        error_msg = f"Failed to get navigation elements: {str(e)}"
        logger.error(f"DOM get_navigation_elements error: {error_msg}")
        return f"❌ {error_msg}"


@tool
def dom_get_media_elements() -> str:
    """
    Get all media elements from the current page.

    Includes:
    - <img>, <video>, <audio>
    - <canvas>, <svg>
    - Media control buttons

    Returns:
        A formatted string listing all media elements.
    """
    try:
        extractor = _get_dom_extractor()
        all_elements = extractor.extract_all_elements()

        media_elements = []
        for elem in all_elements:
            if not elem.is_visible:
                continue

            tag_name = elem.tag_name.lower()
            is_media_element = False

            # Media tags
            if tag_name in ['img', 'video', 'audio', 'canvas', 'svg']:
                is_media_element = True
            elif tag_name == 'object' and elem.attributes.get('type', '').startswith(('image/', 'video/', 'audio/')):
                is_media_element = True

            if is_media_element:
                media_elements.append(elem)

        result = DOMFormatter.format_media_elements(media_elements)
        logger.info(f"DOM get_media_elements: Found {len(media_elements)} media elements")
        return result

    except Exception as e:
        error_msg = f"Failed to get media elements: {str(e)}"
        logger.error(f"DOM get_media_elements error: {error_msg}")
        return f"❌ {error_msg}"


@tool
def dom_get_content_elements(query_text: str = None, max_results: int = 20, similarity_threshold: float = 0.1) -> str:
    """
    Get all visible elements that contain meaningful text content from comprehensive sources.

    Supports semantic similarity-based ranking when query_text is provided.

    Extracts text from multiple sources with priority-based approach:
      - Direct text content (innerText, textContent)
      - Form elements (value, placeholder, label)
      - Media attributes (alt, title, caption)
      - ARIA accessibility attributes
      - Data attributes
      - xpath generated content (::before, ::after)
      - Semantic element context

    Uses intelligent filtering and deduplication for meaningful content.

    Args:
        query_text: Optional query text for semantic similarity matching
        max_results: Maximum number of results to return (default: 20)
        similarity_threshold: Minimum similarity score to include elements (default: 0.1)

    Returns:
        A formatted string listing all visible text-like content with source attribution,
        optionally ranked by semantic similarity to query_text.
    """
    # 全面的文本属性列表，按重要性分组
    COMPREHENSIVE_TEXT_ATTRIBUTES = {
        'form': ['value', 'placeholder', 'label', 'name'],
        'media': ['alt', 'title', 'caption', 'summary'],
        'aria': ['aria-label', 'aria-labelledby', 'aria-describedby', 'aria-valuetext', 'aria-placeholder'],
        'data': ['data-text', 'data-label', 'data-title', 'data-content', 'data-original-title', 'data-tooltip'],
        'semantic': ['cite', 'datetime', 'abbr', 'acronym'],
        'tooltip': ['tooltip', 'hint', 'help-text', 'data-bs-original-title']
    }

    # 语义重要的标签，优先提取
    SEMANTIC_PRIORITY_TAGS = {
        'high': ['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'title', 'label'],
        'medium': ['strong', 'em', 'mark', 'button', 'a', 'th', 'caption', 'legend'],
        'normal': ['p', 'span', 'div', 'td', 'li', 'dt', 'dd']
    }

    def _get_element_info(elem_id: str, all_elements: list) -> str:
        """根据元素ID获取丰富的元素信息"""
        try:
            for elem in all_elements:
                if str(elem.id) == str(elem_id):
                    tag_name = elem.tag_name.lower() if elem.tag_name else 'unknown'

                    # 收集属性信息
                    info_parts = [tag_name]

                    # 检查交互性
                    if elem.is_interactive:
                        info_parts.append('interactive')

                    # 检查具体的交互属性
                    if elem.attributes:
                        attrs = elem.attributes

                        # 检查是否可点击
                        if ('onclick' in attrs or
                                'href' in attrs or
                                attrs.get('role') in ['button', 'link'] or
                                attrs.get('cursor') == 'pointer' or
                                'data-click' in str(attrs).lower()):
                            info_parts.append('clickable')

                        # 检查特殊角色
                        if 'role' in attrs:
                            role = attrs['role']
                            if role not in ['button', 'link']:  # 避免重复
                                info_parts.append(f'role-{role}')

                        # 检查表单元素
                        if tag_name in ['input', 'select', 'textarea']:
                            input_type = attrs.get('type', 'text')
                            info_parts.append(f'type-{input_type}')

                    return '|'.join(info_parts)
            return 'unknown'
        except Exception:
            return 'unknown'

    def _format_display_text(text: str) -> str:
        """格式化显示文本，处理空格和换行"""
        if not text:
            return ""

        import re

        # 1. 规范化空白字符：将多个空格、制表符、换行符等替换为单个空格
        formatted_text = re.sub(r'\s+', ' ', text.strip())

        # 2. 处理中文标点后的空格问题
        formatted_text = re.sub(r'([。，、；：！？]) +', r'\1 ', formatted_text)

        # 3. 处理数字和单位之间的空格（如：9 . 6 分 -> 9.6分）
        formatted_text = re.sub(r'(\d+)\s*\.\s*(\d+)\s*分', r'\1.\2分', formatted_text)

        # 4. 处理电影名称和评分之间的格式（如：电影名 . 分 -> 电影名 评分）
        formatted_text = re.sub(r'([^\d\s])\s*\.\s*分', r'\1 评分', formatted_text)

        # 5. 处理万、亿等单位前的空格
        formatted_text = re.sub(r'(\d+)\s*\.\s*(\d*)\s*(万|亿)', r'\1.\2\3', formatted_text)

        # 6. 处理序号格式（如：2 电影名 -> 2. 电影名）
        formatted_text = re.sub(r'^(\d+)\s+([^\d])', r'\1. \2', formatted_text)
        formatted_text = re.sub(r'(\s)(\d+)\s+([^\d])', r'\1\2. \3', formatted_text)

        # 7. 清理多余的空格
        formatted_text = re.sub(r'\s+', ' ', formatted_text).strip()

        return formatted_text

    def _extract_element_texts(elem):
        """从单个元素提取所有可能的文本内容"""
        texts = []
        tag_name = elem.tag_name.lower() if elem.tag_name else ''

        # 1. 直接文本内容 - 优先使用text_content，如果为空则尝试其他方法
        direct_text = elem.text_content.strip() if elem.text_content else ''
        if direct_text:
            # 确定优先级
            priority = 'high' if tag_name in SEMANTIC_PRIORITY_TAGS['high'] else \
                'medium' if tag_name in SEMANTIC_PRIORITY_TAGS['medium'] else 'normal'
            texts.append((f'text[{priority}]', direct_text))

        # 2. 属性文本 - 按类别提取
        if hasattr(elem, 'attributes') and elem.attributes:
            attrs = elem.attributes

            for category, attr_list in COMPREHENSIVE_TEXT_ATTRIBUTES.items():
                for attr_name in attr_list:
                    value = attrs.get(attr_name, '').strip()
                    if value and value != direct_text:  # 避免与直接文本重复
                        texts.append((f'{category}:{attr_name}', value))

        # 3. 特殊处理某些标签类型
        if tag_name == 'input' and hasattr(elem, 'attributes') and elem.attributes:
            input_type = elem.attributes.get('type', '').lower()
            if input_type in ['submit', 'button', 'reset']:
                value = elem.attributes.get('value', '').strip()
                if value and value not in [t[1] for t in texts]:
                    texts.append(('input:button', value))

        # 4. 链接和按钮的特殊处理
        if tag_name in ['a', 'button'] and hasattr(elem, 'attributes') and elem.attributes:
            href = elem.attributes.get('href', '').strip()
            if href and href.startswith('#') and len(href) > 1:
                # 可能是有意义的锚点
                texts.append(('link:anchor', href[1:]))

        return texts

    def _calculate_element_priority(elem) -> int:
        """
        计算元素优先级分数

        Args:
            elem: ElementInfo 对象

        Returns:
            优先级分数（越高越重要）
        """
        priority = 0
        tag_name = elem.tag_name.lower()

        # 基础标签优先级
        tag_priorities = {
            'button': 100, 'input': 90, 'a': 80, 'select': 85,
            'textarea': 75, 'form': 70, 'h1': 60, 'h2': 55,
            'h3': 50, 'label': 45, 'span': 30, 'div': 20, 'p': 25
        }
        priority += tag_priorities.get(tag_name, 10)

        # 交互性加分
        if elem.is_interactive:
            priority += 50

        # 可见性加分
        if elem.is_visible:
            priority += 30

        # 在视口内加分
        if elem.is_in_viewport:
            priority += 20

        # 文本内容长度加分（适中长度最好）
        text_len = len(elem.text_content) if elem.text_content else 0
        if 5 <= text_len <= 50:
            priority += 15
        elif 1 <= text_len <= 100:
            priority += 10

        return priority

    def calculate_batch_similarities(query_text: str, element_objects: set, client, model: str = "gpt-4o-mini") -> list:
        """
        使用大模型直接进行语义相似性排序

        Args:
            query_text: 查询文本
            element_objects: ElementInfo 对象的集合
            client: OpenAI客户端
            model: 大模型名称

        Returns:
            按相似性排序的元素列表 [(elem_id, xpath, source, text, priority, similarity), ...]
        """
        if not element_objects:
            return []

        try:
            # 1. 从 ElementInfo 对象中提取文本信息
            element_data = []
            for elem in element_objects:
                if not elem.is_visible or not elem.text_content:
                    continue

                # 提取元素的多种文本信息
                texts = _extract_element_texts(elem)
                if not texts:
                    continue

                # 合并所有文本信息
                combined_text = " | ".join([f"{source}: {text}" for source, text in texts])

                element_data.append({
                    'id': elem.id,
                    'xpath': elem.xpath,
                    'text': combined_text[:1000],  # 限制长度
                    'tag': elem.tag_name,
                    'attributes': str(elem.attributes)[:200] if elem.attributes else ""
                })

            if not element_data:
                return []

            logger.info(f"Using LLM to rank {len(element_data)} elements for query: {query_text}")

            # 2. 构建给大模型的提示
            elements_text = ""
            for i, elem in enumerate(element_data):
                elements_text += f"{i+1}. ID:{elem['id']} | Tag:{elem['tag']} | Text:{elem['text']}\n"

            prompt = f"""
你是一个专业的网页元素匹配专家。请根据用户查询对以下网页元素进行相似性排序。

用户查询: "{query_text}"

网页元素列表:
{elements_text}

请按照与查询最相关的顺序，返回元素ID列表，格式如下：
[元素ID1, 元素ID2, 元素ID3, ...]

只返回JSON格式的ID数组，不要其他解释。最多返回前20个最相关的元素。
"""

            # 3. 调用大模型进行排序
            response = client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "system", "content": "你是一个专业的网页元素匹配专家，擅长理解用户意图并找到最相关的网页元素。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=500
            )

            # 4. 解析大模型返回的排序结果
            import json
            try:
                ranked_ids = json.loads(response.choices[0].message.content.strip())
                if not isinstance(ranked_ids, list):
                    raise ValueError("返回格式不是列表")
            except (json.JSONDecodeError, ValueError) as e:
                logger.error(f"Failed to parse LLM ranking result: {e}")
                # 降级到原始顺序
                ranked_ids = [elem['id'] for elem in element_data]

            # 5. 根据排序结果构建返回数据
            id_to_elem = {elem['id']: elem for elem in element_data}
            id_to_object = {elem.id: elem for elem in element_objects}

            ranked_results = []
            for rank, elem_id in enumerate(ranked_ids):
                if elem_id in id_to_elem and elem_id in id_to_object:
                    elem_data = id_to_elem[elem_id]
                    elem_obj = id_to_object[elem_id]

                    # 计算相似性分数（基于排名，第一名=1.0，依次递减）
                    similarity = max(0.1, 1.0 - (rank * 0.05))

                    # 提取优先级（基于元素类型）
                    priority = _calculate_element_priority(elem_obj)

                    ranked_results.append((
                        elem_id,
                        elem_obj.xpath,
                        f"{elem_obj.tag_name}",
                        elem_data['text'],
                        priority,
                        similarity
                    ))

            logger.info(f"LLM ranking completed, returned {len(ranked_results)} ranked elements")
            return ranked_results

        except Exception as e:
            logger.error(f"LLM similarity ranking failed: {e}")
            # 降级到关键词匹配
            from ..dom.utils.text_utils import TextUtils
            fallback_results = []
            for elem in element_objects:
                if not elem.is_visible or not elem.text_content:
                    continue
                similarity = TextUtils.calculate_text_similarity(query_text, elem.text_content)
                priority = _calculate_element_priority(elem)
                fallback_results.append((elem.id, elem.xpath, elem.tag_name, elem.text_content, priority, similarity))

            # 按相似性排序
            fallback_results.sort(key=lambda x: x[5], reverse=True)
            return fallback_results[:20]  # 限制返回数量

    try:
        extractor = _get_dom_extractor()
        all_elements = extractor.extract_all_elements()

        content_items = []  # 存储 (id, xpath, source, text, priority) 五元组
        global_seen_texts = set()  # 全局去重

        for elem in all_elements:
            if not elem.is_visible:
                continue
            if not elem.text_content:
                continue
            global_seen_texts.add(elem)
        # 如果提供了查询文本，使用语义相似性重新排序
        if query_text and query_text.strip():
            logger.info(f"Using batch semantic similarity matching with query: {query_text}")

            try:
                from openai import OpenAI
                from ..core.config import config

                # 创建OpenAI客户端
                client_config = {}
                if hasattr(config, 'OPENAI_API_KEY'):
                    client_config['api_key'] = config.OPENAI_API_KEY
                if hasattr(config, 'OPENAI_API_BASE') and config.OPENAI_API_BASE:
                    client_config['base_url'] = config.OPENAI_API_BASE

                client = OpenAI(**client_config)
                embedding_model = "859-text-embedding-3-small__1"

                # 批量计算语义相似性（只需1次API调用！）
                similarity_results = calculate_batch_similarities(
                    query_text.strip(),
                global_seen_texts,
                client,
                embedding_model
                )

                # 过滤和增强结果
                enhanced_items = []
                for elem_id, xpath, source, text, base_priority, similarity in similarity_results:
                    # 只保留相似性超过阈值的元素
                    if similarity >= similarity_threshold:
                        # 新的相关性分数 = 基础分数 + 语义相似性分数
                        relevance_score = base_priority + (similarity * 300)  # 语义相似性权重很高
                        enhanced_items.append((elem_id, xpath, source, text, relevance_score, similarity))

            except Exception as e:
                logger.error(f"Batch semantic similarity processing failed: {e}")
                # 降级到原有逻辑
                enhanced_items = [(elem_id, xpath, source, text, base_priority, 0.0)
                                  for elem_id, xpath, source, text, base_priority in content_items]

            # 按相关性分数排序：相关性 > 相似性 > 文本长度
            enhanced_items.sort(key=lambda x: (-x[4], -x[5], -len(x[3])))

            # 限制结果数量
            enhanced_items = enhanced_items[:max_results]

            # 格式化输出（包含相似性分数）
            lines = []
            for elem_id, xpath, source, text, _, similarity in enhanced_items:
                element_info = _get_element_info(elem_id, all_elements)
                display_text = _format_display_text(text)
                lines.append(f"[{source:<15}] [{element_info}] {display_text} [Sim: {similarity:.3f}] [XPath: {xpath}]")

            if not lines:
                return f"❌ No elements found with similarity >= {similarity_threshold} to query: '{query_text}'"

        else:
            # 没有查询文本时使用原有逻辑
            # 智能排序：优先级权重 > 文本长度 > 元素ID
            content_items.sort(key=lambda x: (-x[4], -len(x[3]), int(x[0])))

            # 限制结果数量
            content_items = content_items[:max_results]

            # 格式化输出
            lines = []
            for elem_id, xpath, source, text, _ in content_items:
                element_info = _get_element_info(elem_id, all_elements)
                display_text = _format_display_text(text)
                lines.append(f"[{source:<15}] [{element_info}] {display_text} [XPath: {xpath}]")

        if not lines:
            if query_text:
                return f"❌ No elements found matching query: '{query_text}'"
            else:
                return "❌ No visible text content found in any element."

        result = "\n".join(lines)

        # 更新日志信息
        if query_text and query_text.strip():
            logger.info(
                f"DOM get_content_elements: Found {len(lines)} semantically relevant items (similarity >= {similarity_threshold}) from {len(all_elements)} total elements for query: '{query_text}'")
        else:
            logger.info(
                f"DOM get_content_elements: Found {len(lines)} unique visible text items from {len(all_elements)} total elements.")

        return result

    except Exception as e:
        error_msg = f"Failed to extract content elements: {str(e)}"
        logger.error(f"DOM get_content_elements error: {error_msg}")
        return f"❌ {error_msg}"


# =============================================================================
# FUNCTIONAL LAYER - By user intent
# =============================================================================

@tool
def dom_get_clickable_elements(
        exclude_links: bool = False
) -> str:
    """
    Get all clickable elements from the current page.

    Intelligently identifies:
    - Standard clickable elements
    - Elements with click event handlers
    - Clickable custom components

    Args:
        exclude_links: Whether to exclude link elements

    Returns:
        A formatted string listing all clickable elements.
    """
    try:
        extractor = _get_dom_extractor()
        all_elements = extractor.extract_all_elements()

        clickable_elements = []
        for elem in all_elements:
            if not elem.is_visible:
                continue

            tag_name = elem.tag_name.lower()
            is_clickable = False

            # Standard clickable elements
            if tag_name in ['button', 'a']:
                if not (exclude_links and tag_name == 'a'):
                    is_clickable = True
            elif tag_name == 'input' and elem.attributes.get('type', '').lower() in ['button', 'submit', 'reset',
                                                                                     'checkbox', 'radio']:
                is_clickable = True
            elif elem.attributes.get('role') in ['button', 'link', 'menuitem', 'tab', 'option']:
                is_clickable = True
            elif elem.attributes.get('onclick') or elem.attributes.get('data-action'):
                is_clickable = True
            elif elem.is_interactive:  # Trust JavaScript detection
                is_clickable = True

            # Additional heuristics for custom clickable elements
            if not is_clickable:
                classes = elem.attributes.get('class', '').lower()
                if any(clickable_class in classes for clickable_class in ['btn', 'button', 'clickable', 'click']):
                    is_clickable = True

            if is_clickable:
                clickable_elements.append(elem)

        result = DOMFormatter.format_clickable_elements(clickable_elements)
        logger.info(f"DOM get_clickable_elements: Found {len(clickable_elements)} clickable elements")
        return result

    except Exception as e:
        error_msg = f"Failed to get clickable elements: {str(e)}"
        logger.error(f"DOM get_clickable_elements error: {error_msg}")
        return f"❌ {error_msg}"


@tool
def dom_get_focusable_elements() -> str:
    """
    Get all focusable elements from the current page.

    Includes:
    - Elements with tabindex >= 0
    - Form controls
    - Links and buttons

    Returns:
        A formatted string listing all focusable elements.
    """
    try:
        extractor = _get_dom_extractor()
        all_elements = extractor.extract_all_elements()

        focusable_elements = []
        for elem in all_elements:
            if not elem.is_visible:
                continue

            tag_name = elem.tag_name.lower()
            is_focusable = False

            # Elements with explicit tabindex
            tabindex = elem.attributes.get('tabindex')
            if tabindex is not None:
                try:
                    if int(tabindex) >= 0:
                        is_focusable = True
                except ValueError:
                    pass

            # Naturally focusable elements
            if tag_name in ['input', 'textarea', 'select', 'button', 'a']:
                # Check if not disabled
                if elem.attributes.get('disabled') is None:
                    is_focusable = True
            elif elem.attributes.get('contenteditable') == 'true':
                is_focusable = True

            if is_focusable:
                focusable_elements.append(elem)

        result = DOMFormatter.format_focusable_elements(focusable_elements)
        logger.info(f"DOM get_focusable_elements: Found {len(focusable_elements)} focusable elements")
        return result

    except Exception as e:
        error_msg = f"Failed to get focusable elements: {str(e)}"
        logger.error(f"DOM get_focusable_elements error: {error_msg}")
        return f"❌ {error_msg}"


@tool
def dom_get_interactive_elements_smart() -> str:
    """
    Get all interactive elements using the most intelligent detection.

    This is the smartest function that combines multiple detection strategies:
    - HTML semantics
    - ARIA roles
    - Event listeners
    - Visual characteristics
    - Context analysis

    Returns:
        A formatted string listing all interactive elements sorted by priority.
    """
    try:
        extractor = _get_dom_extractor()
        all_elements = extractor.extract_all_elements()

        interactive_elements = []
        for elem in all_elements:
            if not elem.is_visible:
                continue

            # Use the JavaScript is_interactive as primary filter
            if elem.is_interactive:
                interactive_elements.append(elem)
                continue

            # Additional smart detection for elements that might be missed
            tag_name = elem.tag_name.lower()

            # High-confidence interactive elements
            if tag_name in ['a', 'button', 'input', 'select', 'textarea']:
                if elem.attributes.get('disabled') is None:
                    interactive_elements.append(elem)
                    continue

            # Elements with clear interactive attributes
            if (elem.attributes.get('onclick') or
                    elem.attributes.get('role') in ['button', 'link', 'menuitem', 'tab', 'option', 'checkbox',
                                                    'radio'] or
                    elem.attributes.get('tabindex') is not None or
                    elem.attributes.get('data-action') or
                    elem.attributes.get('data-toggle') or
                    elem.attributes.get('aria-haspopup')):
                interactive_elements.append(elem)
                continue

            # Context-based detection
            classes = elem.attributes.get('class', '').lower()
            if any(interactive_class in classes for interactive_class in
                   ['btn', 'button', 'clickable', 'menu-item', 'nav-link', 'dropdown-toggle']):
                interactive_elements.append(elem)

        result = DOMFormatter.format_interactive_elements(interactive_elements)
        logger.info(f"DOM get_interactive_elements_smart: Found {len(interactive_elements)} interactive elements")
        return result

    except Exception as e:
        error_msg = f"Failed to get interactive elements: {str(e)}"
        logger.error(f"DOM get_interactive_elements_smart error: {error_msg}")
        return f"❌ {error_msg}"


# =============================================================================
# ANALYSIS LAYER - Information quality assessment
# =============================================================================

@tool
def dom_assess_information_completeness(
        user_query: str,
        current_results_count: int,
        check_navigation: bool = True
) -> str:
    """
    使用LLM智能评估当前页面信息的完整性。

    采用直接LLM评估方法，能够：
    1. 理解用户查询的真实意图和期望
    2. 分析当前结果是否满足完整性要求
    3. 检测页面中可能存在更多数据的线索
    4. 提供具体的改进建议

    Args:
        user_query: 用户的原始查询文本
        current_results_count: 当前获取到的结果数量
        check_navigation: 是否检查导航元素（默认True）

    Returns:
        LLM评估的完整性分析结果
    """
    try:
        from langchain_openai import ChatOpenAI
        from ..core.config import config

        # 获取页面上下文信息
        page_context = ""
        if check_navigation:
            try:
                # 获取可能的导航线索
                extractor = _get_dom_extractor()
                all_elements = extractor.extract_all_elements()

                navigation_clues = []
                for elem in all_elements[:50]:  # 限制检查前50个元素以提高性能
                    if not elem.is_visible:
                        continue

                    text = elem.text_content.strip().lower() if elem.text_content else ""
                    if any(keyword in text for keyword in [
                        "更多", "下一页", "查看全部", "完整", "展开", "加载更多",
                        "next", "more", "load more", "show all", "view all", "expand"
                    ]):
                        navigation_clues.append(f"- {elem.tag_name}: '{elem.text_content.strip()[:50]}'")

                if navigation_clues:
                    page_context = f"页面中发现可能的导航线索：\n" + "\n".join(navigation_clues[:5])
                else:
                    page_context = "页面中未发现明显的导航线索"

            except Exception as e:
                page_context = f"无法获取页面上下文：{str(e)}"

        # 构建LLM评估提示
        prompt = f"""
你是一个数据完整性评估专家。请分析以下信息提取任务的完整性：

**用户查询**：{user_query}
**当前结果数量**：{current_results_count}
**页面上下文**：{page_context}

请从以下维度评估数据完整性：

1. **用户期望分析**：用户真正想要什么信息？期望多少数据？
2. **字段完整性**：用户要求的字段/属性是否都能从当前结果中获得？
3. **数量充分性**：当前数据量是否满足用户的"完整"要求？
4. **扩展可能性**：页面是否暗示还有更多相关数据？

**输出要求**：
- 如果信息完整，输出：COMPLETE - [简要说明原因]
- 如果信息不完整，输出：INCOMPLETE - [具体说明缺失什么] - [建议下一步操作]

**分析要点**：
- 注意"完整榜单"、"全部"、"所有"等关键词
- 考虑用户查询中明确或隐含的数量期望
- 分析页面线索是否暗示有更多数据
- 评估数据质量是否符合用户需求

请给出你的评估结果：
"""

        # 调用LLM进行评估
        llm_config = config.get_llm_config()
        llm_client = ChatOpenAI(**llm_config)
        response = llm_client.invoke(prompt)

        # 提取响应内容
        response_text = response.content if hasattr(response, 'content') else str(response)

        # 格式化返回结果
        if response_text.strip().upper().startswith('COMPLETE'):
            result = f"✅ 信息完整性评估：{response_text}"
        else:
            result = f"❌ 信息完整性评估：{response_text}"

        logger.info(
            f"DOM assess_information_completeness: Query='{user_query}', Count={current_results_count}, Result='{response_text[:100]}'")
        return result

    except Exception as e:
        error_msg = f"Failed to assess information completeness: {str(e)}"
        logger.error(f"DOM assess_information_completeness error: {error_msg}")
        return f"❌ {error_msg}"


# =============================================================================
# ENHANCED TOOLS LIST
# =============================================================================

ENHANCED_DOM_TOOLS = [
    # Classification Layer - By HTML tag
    dom_get_buttons_enhanced,
    dom_get_inputs_enhanced,
    dom_get_links_enhanced,

    # Classification Layer - By functional role
    dom_get_form_elements,
    dom_get_navigation_elements,
    dom_get_media_elements,
    dom_get_content_elements,

    # Functional Layer - By user intent
    dom_get_clickable_elements,
    dom_get_focusable_elements,
    dom_get_interactive_elements_smart,

    # Analysis Layer - Information quality assessment
    dom_assess_information_completeness,
]


def get_enhanced_dom_tools() -> List:
    """
    Get a list of enhanced DOM tools with layered architecture.

    Returns:
        A list of enhanced DOM tool functions.
    """
    return ENHANCED_DOM_TOOLS.copy()
