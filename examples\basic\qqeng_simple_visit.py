#!/usr/bin/env python3
"""
QQEng英文学习站点简单访问示例

这是一个简单的示例，演示如何：
1. 访问QQEng英文学习网站
2. 获取基本的报名和上课信息
3. 保存截图

适合初学者快速了解如何使用iICrawlerMCP进行网站信息收集。
"""

import sys
import os
import datetime

# Add the src directory to the path so we can import our package
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'src'))

from iicrawlermcp.agents import build_agent
from iicrawlermcp.core.config import config
import logging

# Set up logging - 记录到文件和控制台
log_filename = f"qqeng_visit_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_filename, encoding='utf-8'),  # 保存到文件
        logging.StreamHandler()  # 显示在控制台
    ]
)
logger = logging.getLogger(__name__)

# 记录日志文件位置
logger.info(f"日志将保存到文件: {log_filename}")


def simple_qqeng_visit():
    """
    简单访问QQEng网站并获取基本信息
    """
    print("开始访问QQEng英文学习网站...")

    # 基本配置
    config.HEADLESS = False  # 显示浏览器
    config.BROWSER_TIMEOUT = 20000  # 20秒超时

    # 创建爬虫代理
    agent = build_agent()

    try:
        # 步骤1: 导航到登录页面
        # logger.info("步骤1: 导航到登录页面")
        # result1 = agent.invoke("访问baidu.com,然后搜索deepseek,进入deepseek官网,询问deepseek一个问题,你是谁,然后告诉我结果")
        # result1 = agent.invoke("visit https://adult.kuaikuenglish.com/k/login/,<NAME_EMAIL>,password 13849857524,登录之后,查看我剩余的课时点")
        # result1 = agent.invoke("访问https://www.maoyan.com/,找到top100栏目电影的前20个的主要信息")
        result1 = agent.invoke("访问https://top.baidu.com/board,找到小说榜完整小说榜单的排名,书名,作者,类型,介绍,返回一个完整的列表")
        # result1 = agent.invoke("访问百度https://www.baidu.com/,在这个页面总结一下今天的热搜,有哪些分类情况")
        logger.info(f"导航结果: {result1['output']}")

    except Exception as e:
        print(f"\n❌ 访问过程中出现错误: {e}")
        logger.error(f"错误详情: {e}", exc_info=True)

    finally:
        # 清理资源
        try:
            agent.cleanup()
            print("\n🧹 资源清理完成")
        except:
            pass


def main():
    """主函数"""
    # 询问用户是否继续
    try:
        simple_qqeng_visit()
    except KeyboardInterrupt:
        print("\n👋 用户取消了操作")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")


if __name__ == "__main__":
    main()
