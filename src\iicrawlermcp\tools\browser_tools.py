"""
Unified Browser Tools for iICrawlerMCP.

This module provides a comprehensive set of browser automation tools organized
in a toolkit pattern following industry best practices from LangChain, CrewAI,
and Playwright frameworks.

The tools are organized in two tiers:
- Basic Tools: Simple, reliable operations for specialized agents
- Advanced Tools: Feature-rich operations for general-purpose agents
"""

import logging
from typing import Optional, List
from langchain_core.tools import tool, BaseTool
from ..core.browser import get_global_browser

logger = logging.getLogger(__name__)


# =============================================================================
# BASIC BROWSER TOOLS - Simple, reliable operations for specialized agents
# =============================================================================

@tool
def navigate_browser(url: str) -> str:
    """
    Navigate the browser to the specified URL.

    Args:
        url: The URL to navigate to (e.g., "https://google.com")

    Returns:
        A success message indicating navigation completion.

    Example:
        navigate_browser("https://google.com")
    """
    try:
        browser = get_global_browser()
        browser.navigate(url)
        success_msg = f"🌐 Successfully navigated to: {url}"
        logger.info(f"BrowserTool navigate: {success_msg}")
        return success_msg
    except Exception as e:
        error_msg = f"Navigation failed: {str(e)}"
        logger.error(f"BrowserTool navigate error: {error_msg}")
        return f"❌ {error_msg}"


@tool
def take_screenshot(
    filename: Optional[str] = None,
    full_page: bool = True
) -> str:
    """
    Take a screenshot of the current page and save to screenshots directory.

    Screenshots are automatically saved to the project's screenshots directory.
    If no filename is provided, an auto-generated timestamp-based name is used.

    Args:
        filename: File name to save the screenshot to (without path).
                 Auto-generated timestamp name if not specified.
        full_page: When true, takes a screenshot of the full scrollable page.
                  When false, captures only the visible viewport.

    Returns:
        A success message with the full screenshot path.

    Example:
        take_screenshot("google.png")
        take_screenshot("homepage.png", full_page=False)
        take_screenshot()  # Auto-generated filename
    """
    try:
        import os
        from datetime import datetime

        # 确保screenshots目录存在
        screenshots_dir = os.path.join(os.getcwd(), "screenshots")
        os.makedirs(screenshots_dir, exist_ok=True)

        # 如果没有提供文件名，生成时间戳文件名
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"screenshot_{timestamp}.png"

        # 确保文件名有.png扩展名
        if not filename.lower().endswith('.png'):
            filename += '.png'

        # 构建完整路径
        full_path = os.path.join(screenshots_dir, filename)

        browser = get_global_browser()
        result_path = browser.screenshot(path=full_path, full_page=full_page)
        success_msg = f"📸 Screenshot saved: {result_path}"
        logger.info(f"BrowserTool screenshot: {success_msg}")
        return success_msg
    except Exception as e:
        error_msg = f"Screenshot failed: {str(e)}"
        logger.error(f"BrowserTool screenshot error: {error_msg}")
        return f"❌ {error_msg}"


@tool
def analyze_screenshot(
    task_description: str,
    filename: Optional[str] = None
) -> str:
    """
    Take a screenshot and analyze it using AI to extract information or find elements.

    This function combines screenshot capture with intelligent AI analysis to:
    - Extract text content from images
    - Identify and locate UI elements
    - Analyze page layout and structure
    - Find specific elements by description
    - Provide visual context for automation tasks

    Args:
        task_description: Detailed description of what to analyze in the screenshot.
                         Examples: "找到登录按钮", "提取页面主要文字内容",
                                 "分析页面布局", "寻找搜索框位置"
        filename: Optional filename for the screenshot. Auto-generated if not provided.

    Returns:
        AI analysis results based on the screenshot and task description.

    Example:
        analyze_screenshot("找到页面上的登录按钮，描述其位置和外观")
        analyze_screenshot("提取页面中所有可见的文字内容")
        analyze_screenshot("分析这个表单的结构和字段", "form_analysis.png")
    """
    try:
        # 1. 先截图
        logger.info(f"Taking screenshot for analysis: {task_description}")
        screenshot_result = take_screenshot.invoke({"filename": filename, "full_page": True})

        if "❌" in screenshot_result:
            return f"❌ Screenshot failed, cannot analyze: {screenshot_result}"

        # 2. 提取截图路径
        import re
        path_match = re.search(r'Screenshot saved: (.+)', screenshot_result)
        if not path_match:
            return f"❌ Could not extract screenshot path from: {screenshot_result}"

        screenshot_path = path_match.group(1)

        # 3. 使用delegation工具进行AI分析
        from .delegation_tools import smart_element_finder

        analysis_prompt = f"""
请基于当前页面的视觉内容分析以下任务：

**任务描述：** {task_description}

**分析要求：**
1. 观察页面中的所有可见元素和内容
2. 根据任务描述重点分析相关内容
3. 提供详细、准确的分析结果
4. 如果是寻找元素，请描述元素的位置、外观特征
5. 如果是提取文字，请尽可能完整地列出所有文字内容
6. 如果是分析布局，请描述页面结构和组织方式

**输出格式：**
- 📋 **分析结果：** [主要发现]
- 📍 **位置信息：** [如果适用，描述元素位置]
- 🎨 **外观特征：** [如果适用，描述视觉特征]
- 📝 **详细描述：** [补充信息和上下文]

**注意：** 截图已保存到 {screenshot_path}，请基于当前页面的实际内容进行分析。
如果无法直接访问截图，请使用DOM工具获取页面信息进行分析。
"""

        logger.info("Analyzing page content with AI")
        analysis_result = smart_element_finder(analysis_prompt)

        # 4. 格式化结果
        if isinstance(analysis_result, dict):
            output = analysis_result.get('output', str(analysis_result))
        else:
            output = str(analysis_result)

        formatted_result = f"""🔍 **截图分析结果**

📸 **截图信息：** {screenshot_result}

🤖 **AI分析：**
{output}

---
*基于页面内容的AI分析结果（截图已保存到 {screenshot_path}）*
"""

        logger.info("Screenshot analysis completed successfully")
        return formatted_result

    except ImportError as e:
        return f"❌ Screenshot analysis unavailable: Missing AI analysis capabilities ({str(e)})"
    except Exception as e:
        error_msg = f"Screenshot analysis failed: {str(e)}"
        logger.error(f"BrowserTool analyze_screenshot error: {error_msg}")
        return f"❌ {error_msg}"


@tool
def get_page_info() -> str:
    """
    Get information about the current page including title and URL.

    Returns:
        A string containing the page title and current URL.

    Example:
        get_page_info()
    """
    try:
        browser = get_global_browser()
        page_info = browser.get_page_info()
        title = page_info.get('title', 'N/A')
        url = page_info.get('url', 'N/A')
        result = f"📄 Page Title: {title}\n🔗 Current URL: {url}"
        logger.info(f"BrowserTool page_info: {result}")
        return result
    except Exception as e:
        error_msg = f"Failed to get page info: {str(e)}"
        logger.error(f"BrowserTool page_info error: {error_msg}")
        return f"❌ {error_msg}"


@tool
def click_element(element_selector: str, description: str = "") -> str:
    """
    Click on an element using complete XPath selector.

    Args:
        element_selector: complete XPath selector for the element (must be obtained from DOM tools first)
        description: Human-readable description of the element

    Returns:
        A success message indicating click completion.

    Example:
        click_element("html/body/div[1]/form/button", "submit button")
    """
    try:
        browser = get_global_browser()
        browser.click(element_selector)
        desc = f" ({description})" if description else ""
        success_msg = f"👆 Clicked element: {element_selector}{desc}"
        logger.info(f"BrowserTool click: {success_msg}")
        return success_msg
    except Exception as e:
        error_msg = f"Click failed: {str(e)}"
        logger.error(f"BrowserTool click error: {error_msg}")
        return f"❌ {error_msg}"


@tool
def type_text(
    element_selector: str,
    text: str,
    description: str = "",
    clear_first: bool = True
) -> str:
    """
    Type text into an element using XPath selector.

    Args:
        element_selector: XPath selector for the element (must be obtained from DOM tools first)
        text: Text to type into the element
        description: Human-readable description of the element
        clear_first: Whether to clear existing text first

    Returns:
        A success message indicating typing completion.

    Example:
        type_text("//input[@id='username']", "john_doe", "username field")
        type_text("html/body/div[1]/form/input[@name='email']", "<EMAIL>", "email input")
    """
    try:
        browser = get_global_browser()
        browser.type_text(element_selector, text, clear_first)
        desc = f" ({description})" if description else ""
        success_msg = f"⌨️ Typed '{text}' into: {element_selector}{desc}"
        logger.info(f"BrowserTool type: {success_msg}")
        return success_msg
    except Exception as e:
        error_msg = f"Type text failed: {str(e)}"
        logger.error(f"BrowserTool type error: {error_msg}")
        return f"❌ {error_msg}"


@tool
def hover_element(element_selector: str, description: str = "") -> str:
    """
    Hover over an element using XPath selector.

    Args:
        element_selector: XPath selector for the element to hover over (must be obtained from DOM tools first)
        description: Human-readable description of the element

    Returns:
        A success message indicating hover completion.

    Example:
        hover_element("//div[@id='menu-dropdown']", "dropdown menu")
        hover_element("html/body/div[1]/span[@class='tooltip-trigger']", "help icon")
    """
    try:
        browser = get_global_browser()
        browser.hover(element_selector)
        desc = f" ({description})" if description else ""
        success_msg = f"🖱️ Hovered over: {element_selector}{desc}"
        logger.info(f"BrowserTool hover: {success_msg}")
        return success_msg
    except Exception as e:
        error_msg = f"Hover failed: {str(e)}"
        logger.error(f"BrowserTool hover error: {error_msg}")
        return f"❌ {error_msg}"


# =============================================================================
# ADVANCED BROWSER TOOLS - Feature-rich operations for general-purpose agents
# =============================================================================

@tool
def navigate_browser_advanced(url: str) -> str:
    """
    Navigate the browser to the specified URL (legacy compatibility).

    Args:
        url: The URL to navigate to (e.g., "https://google.com")

    Returns:
        A success message indicating navigation completion.

    Example:
        navigate_browser_advanced("https://google.com")
    """
    try:
        browser = get_global_browser()
        result = browser.navigate(url)
        logger.info(f"BrowserTool navigate_advanced: {result}")
        return result
    except Exception as e:
        error_msg = f"Navigation failed: {str(e)}"
        logger.error(f"BrowserTool navigate_advanced error: {error_msg}")
        return f"❌ {error_msg}"


@tool
def take_screenshot_advanced(
    filename: Optional[str] = None,
    full_page: bool = True,
    element: Optional[str] = None,
    ref: Optional[str] = None
) -> str:
    """
    Take a screenshot of the current page with advanced options.

    Args:
        filename: File name to save the screenshot to. Defaults to page-{timestamp}.png if not specified.
        full_page: When true, takes a screenshot of the full scrollable page, instead of the currently visible viewport. Cannot be used with element screenshots.
        element: Human-readable element description used to obtain permission to screenshot the element. If not provided, the screenshot will be taken of viewport. If element is provided, ref must be provided too.
        ref: Exact target element reference from the page snapshot. If not provided, the screenshot will be taken of viewport. If ref is provided, element must be provided too.

    Returns:
        A success message indicating screenshot completion.

    Example:
        take_screenshot_advanced("google.png")
        take_screenshot_advanced("homepage.png", full_page=False)
        take_screenshot_advanced("element.png", element="login button", ref="#login-btn")
    """
    try:
        browser = get_global_browser()

        # Validate element and ref parameters
        if element and not ref:
            return "❌ Error: ref parameter is required when element is provided"
        if ref and not element:
            return "❌ Error: element parameter is required when ref is provided"
        if ref and full_page:
            return "❌ Error: full_page cannot be used with element screenshots"

        # Call browser.screenshot with correct parameters
        result_path = browser.screenshot(
            path=filename,
            full_page=full_page,
            element_selector=ref  # Use correct parameter name
        )

        # Return success message with emoji
        success_msg = f"📸 Screenshot saved: {result_path}"
        logger.info(f"BrowserTool screenshot_advanced: {success_msg}")
        return success_msg
    except Exception as e:
        error_msg = f"Screenshot failed: {str(e)}"
        logger.error(f"BrowserTool screenshot_advanced error: {error_msg}")
        return f"❌ {error_msg}"


@tool
def browser_snapshot() -> str:
    """
    Capture accessibility snapshot of the current page, this is better than screenshot.

    Returns:
        A string representation of the page snapshot including accessibility information.

    Example:
        browser_snapshot()
    """
    try:
        browser = get_global_browser()
        snapshot_data = browser.snapshot()

        # Format the snapshot data for display
        result = f"📄 Page Snapshot:\n"
        result += f"Title: {snapshot_data.get('title', 'N/A')}\n"
        result += f"URL: {snapshot_data.get('url', 'N/A')}\n"
        result += f"Viewport: {snapshot_data.get('viewport', 'N/A')}\n"
        result += f"Accessibility tree captured successfully"

        logger.info(f"BrowserTool browser_snapshot: {result}")
        return result
    except Exception as e:
        error_msg = f"Snapshot failed: {str(e)}"
        logger.error(f"BrowserTool browser_snapshot error: {error_msg}")
        return f"❌ {error_msg}"


@tool
def click_element_advanced(element: str, ref: str, double_click: bool = False) -> str:
    """
    Perform click on a web page with advanced options.

    Args:
        element: Human-readable element description used to obtain permission to interact with the element.
        ref: Exact XPath selector from DOM tools (must be obtained from DOM tools first).
        double_click: Whether to perform a double click instead of a single click.

    Returns:
        A success message indicating click completion.

    Example:
        click_element_advanced("login button", "//button[@id='login-btn']")
        click_element_advanced("submit button", "html/body/div[1]/form/button[@type='submit']", double_click=True)
    """
    try:
        browser = get_global_browser()
        # Browser.click() takes selector and click_type parameters
        click_type = "double" if double_click else "single"
        result = browser.click(selector=ref, click_type=click_type)

        # Return success message with emoji
        success_msg = f"🖱️ Clicked {element}: {result}"
        logger.info(f"BrowserTool click_advanced: {success_msg}")
        return success_msg
    except Exception as e:
        error_msg = f"Click failed: {str(e)}"
        logger.error(f"BrowserTool click_advanced error: {error_msg}")
        return f"❌ {error_msg}"


@tool
def type_text_advanced(element: str, ref: str, text: str, submit: bool = False) -> str:
    """
    Type text into editable element with advanced options.

    Args:
        element: Human-readable element description used to obtain permission to interact with the element.
        ref: Exact XPath selector from DOM tools (must be obtained from DOM tools first).
        text: Text to type into the element.
        submit: Whether to submit entered text (press Enter after).

    Returns:
        A success message indicating typing completion.

    Example:
        type_text_advanced("username field", "//input[@id='username']", "john_doe")
        type_text_advanced("search box", "html/body/div[1]/form/input[@name='q']", "python tutorial", submit=True)
    """
    try:
        browser = get_global_browser()
        # Browser.type_text() takes selector, text, and clear_first parameters
        browser.type_text(selector=ref, text=text, clear_first=True)

        # Handle submit if requested
        if submit:
            browser.press_key("Enter")

        # Return success message with emoji
        success_msg = f"⌨️ Typed into {element}: '{text}'"
        if submit:
            success_msg += " and submitted"
        logger.info(f"BrowserTool type_advanced: {success_msg}")
        return success_msg
    except Exception as e:
        error_msg = f"Type text failed: {str(e)}"
        logger.error(f"BrowserTool type_advanced error: {error_msg}")
        return f"❌ {error_msg}"


@tool
def browser_wait_for(time: Optional[float] = None, text: Optional[str] = None, text_gone: Optional[str] = None) -> str:
    """
    Wait for text to appear or disappear or a specified time to pass.

    Args:
        time: The time to wait in seconds.
        text: The text to wait for.
        text_gone: The text to wait for to disappear.

    Returns:
        A success message indicating wait completion.

    Example:
        browser_wait_for(time=2.5)
        browser_wait_for(text="Loading complete")
        browser_wait_for(text_gone="Loading...")
    """
    try:
        browser = get_global_browser()

        if time is not None:
            # Simple time-based wait
            import time as time_module
            time_module.sleep(time)
            success_msg = f"⏱️ Waited for {time} seconds"

        elif text is not None:
            # Simplified: just wait 1 second instead of long timeout
            import time as time_module
            time_module.sleep(1)
            success_msg = f"⏱️ Waited 1 second for text '{text}'"

        elif text_gone is not None:
            # Simplified: just wait 1 second instead of long timeout
            import time as time_module
            time_module.sleep(1)
            success_msg = f"⏱️ Waited 1 second for text '{text_gone}' to disappear"

        else:
            # Default wait
            import time as time_module
            time_module.sleep(1)
            success_msg = "⏱️ Waited 1 second (default)"

        logger.info(f"Tool browser_wait_for: {success_msg}")
        return success_msg
    except Exception as e:
        error_msg = f"Wait failed: {str(e)}"
        logger.error(f"Tool browser_wait_for error: {error_msg}")
        return f"❌ {error_msg}"


@tool
def browser_evaluate(function: str, element: Optional[str] = None, ref: Optional[str] = None) -> str:
    """
    Evaluate JavaScript expression on page or element.

    Args:
        function: JavaScript function as string. Use () => { /* code */ } for page-level execution,
                 or (element) => { /* code */ } for element-specific execution.
        element: Human-readable element description. Required when ref is provided.
                Used to obtain permission to interact with the element.
        ref: XPath selector for element targeting. Required when element is provided.
             Can be any valid XPath expression.

    Note:
        - element and ref parameters must be provided together or both omitted
        - When both are provided, the function will be executed on the element found by XPath
        - XPath expressions are evaluated using document.evaluate() with FIRST_ORDERED_NODE_TYPE

    Returns:
        Formatted string with execution result (🔧 prefix for success) or error message (❌ prefix).

    Examples:
        browser_evaluate("() => document.title")
        browser_evaluate("(element) => element.textContent", element="main heading", ref="//h1")
        browser_evaluate("(element) => element.click()", element="更多链接", ref="//a[contains(text(), '更多')]")
    """
    try:
        browser = get_global_browser()

        # Validate element and ref parameters
        if element and not ref:
            return "❌ Error: ref parameter is required when element is provided"
        if ref and not element:
            return "❌ Error: element parameter is required when ref is provided"

        # Browser.evaluate_script() takes script and optional args
        if ref:
            # For element-specific evaluation, use XPath to find the element
            # Create a script that uses document.evaluate() for XPath support
            # Escape single quotes in the XPath to prevent JavaScript syntax errors
            escaped_xpath = ref.replace("'", "\\'")
            script = f"""
            (function() {{
                try {{
                    // Use XPath to find the element
                    const xpath = '{escaped_xpath}';
                    const result = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);
                    const element = result.singleNodeValue;

                    if (!element) {{
                        return null;
                    }}

                    // Execute the provided function on the element
                    return ({function})(element);
                }} catch (error) {{
                    throw new Error('XPath evaluation failed: ' + error.message);
                }}
            }})()
            """
        else:
            script = function

        result = browser.evaluate_script(script)

        # Return success message with result
        success_msg = f"🔧 JavaScript result: {result}"
        logger.info(f"Tool browser_evaluate: {success_msg}")
        return success_msg
    except Exception as e:
        error_msg = f"JavaScript evaluation failed: {str(e)}"
        logger.error(f"Tool browser_evaluate error: {error_msg}")
        return f"❌ {error_msg}"


@tool
def browser_hover(element: str, ref: str) -> str:
    """
    Hover over element on page.

    Args:
        element: Human-readable element description used to obtain permission to interact with the element.
        ref: Exact XPath selector from DOM tools (must be obtained from DOM tools first).

    Returns:
        A success message indicating hover completion.

    Example:
        browser_hover("dropdown menu", "//div[@id='menu-dropdown']")
    """
    try:
        browser = get_global_browser()
        result = browser.hover(element_ref=ref)
        logger.info(f"Tool browser_hover: {result}")
        return result
    except Exception as e:
        error_msg = f"Hover failed: {str(e)}"
        logger.error(f"Tool browser_hover error: {error_msg}")
        return f"❌ {error_msg}"


@tool
def browser_press_key(key: str) -> str:
    """
    Press a key on the keyboard.

    Args:
        key: Name of the key to press or a character to generate, such as ArrowLeft or a.

    Returns:
        A success message indicating key press completion.

    Example:
        browser_press_key("Enter")
        browser_press_key("ArrowDown")
        browser_press_key("a")
    """
    try:
        browser = get_global_browser()
        result = browser.press_key(key=key)
        logger.info(f"Tool browser_press_key: {result}")
        return result
    except Exception as e:
        error_msg = f"Key press failed: {str(e)}"
        logger.error(f"Tool browser_press_key error: {error_msg}")
        return f"❌ {error_msg}"


@tool
def browser_select_option(element: str, ref: str, values: list) -> str:
    """
    Select an option in a dropdown.

    Args:
        element: Human-readable element description used to obtain permission to interact with the element.
        ref: Exact XPath selector from DOM tools (must be obtained from DOM tools first).
        values: Array of values to select in the dropdown. This can be a single value or multiple values.

    Returns:
        A success message indicating selection completion.

    Example:
        browser_select_option("country dropdown", "//select[@id='country']", ["US"])
        browser_select_option("multi-select", "html/body/div[1]/form/select[@name='options']", ["option1", "option2"])
    """
    try:
        browser = get_global_browser()

        # Browser.select_option() expects selector and one of: value, label, or index
        # Since we receive a list of values, we need to handle multiple selections
        if not values:
            raise ValueError("Values list cannot be empty")

        results = []
        for value in values:
            # Try to select by value first
            try:
                result = browser.select_option(selector=ref, value=value)
                results.append(f"Selected value '{value}': {result}")
            except Exception as e:
                # If value selection fails, try by label
                try:
                    result = browser.select_option(selector=ref, label=value)
                    results.append(f"Selected label '{value}': {result}")
                except Exception as e2:
                    results.append(f"Failed to select '{value}': {str(e2)}")

        success_msg = f"🔽 Selection results for {element}:\n" + "\n".join(results)
        logger.info(f"Tool browser_select_option: {success_msg}")
        return success_msg
    except Exception as e:
        error_msg = f"Select option failed: {str(e)}"
        logger.error(f"Tool browser_select_option error: {error_msg}")
        return f"❌ {error_msg}"


# =============================================================================
# BROWSER TOOLKIT - Unified tool management following industry best practices
# =============================================================================

class BrowserToolkit:
    """
    Unified browser toolkit following LangChain/CrewAI patterns.

    Provides tiered access to browser automation tools:
    - Basic Tools: Simple, reliable operations for specialized agents
    - Advanced Tools: Feature-rich operations for general-purpose agents
    """

    @classmethod
    def get_basic_tools(cls) -> List[BaseTool]:
        """
        Get basic browser tools for specialized agents (BrowserAgent, ElementAgent).

        Returns:
            List of basic browser automation tools.
        """
        return [
            navigate_browser,
            take_screenshot,
            analyze_screenshot,
            get_page_info,
            click_element,
            type_text,
            hover_element
        ]

    @classmethod
    def get_advanced_tools(cls) -> List[BaseTool]:
        """
        Get advanced browser tools for general-purpose agents (CrawlerAgent).

        Returns:
            List of advanced browser automation tools with rich features.
        """
        return [
            navigate_browser_advanced,
            take_screenshot_advanced,
            analyze_screenshot,
            browser_snapshot,
            click_element_advanced,
            type_text_advanced,
            browser_wait_for,
            browser_evaluate,
            browser_hover,
            browser_press_key,
            browser_select_option
        ]

    @classmethod
    def get_all_tools(cls) -> List[BaseTool]:
        """
        Get all available browser tools.

        Returns:
            Complete list of browser automation tools.
        """
        return cls.get_basic_tools() + cls.get_advanced_tools()


# Legacy compatibility functions
def get_tools() -> List[BaseTool]:
    """
    Get all browser tools (legacy compatibility).

    Returns:
        Complete list of browser automation tools.
    """
    return BrowserToolkit.get_all_tools()


def get_browser_specific_tools() -> List[BaseTool]:
    """
    Get browser-specific tools (legacy compatibility).

    Returns:
        List of basic browser tools.
    """
    return BrowserToolkit.get_basic_tools()


def cleanup_tools() -> None:
    """
    Clean up resources used by tools (e.g., close browser).

    This should be called when the application is shutting down
    or when tools are no longer needed.
    """
    try:
        from ..core.browser import close_global_browser
        close_global_browser()
        logger.info("Browser tools cleanup completed")
    except Exception as e:
        logger.error(f"Error during browser tools cleanup: {e}")


# Export the toolkit class and legacy functions
__all__ = [
    'BrowserToolkit',
    'get_tools',
    'get_browser_specific_tools',
    'cleanup_tools'
]
