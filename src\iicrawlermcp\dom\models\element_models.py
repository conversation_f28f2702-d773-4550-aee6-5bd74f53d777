"""
DOM element models using Pydantic for structured output.

This module provides unified data models for different types of DOM elements,
ensuring consistent formatting and type safety across all DOM tools.
"""

from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List


class DOMElementOutput(BaseModel):
    """统一的DOM元素输出模型"""
    highlight_index: Optional[int] = Field(description="元素高亮索引")
    text_content: str = Field(description="元素文本内容")
    xpath: str = Field(description="元素XPath路径")
    tag_name: str = Field(description="HTML标签名")
    attributes: Dict[str, Any] = Field(default_factory=dict, description="元素属性")
    is_visible: bool = Field(description="是否可见")
    is_interactive: bool = Field(default=False, description="是否可交互")
    is_in_viewport: bool = Field(default=False, description="是否在视口内")

    def _get_element_function_description(self) -> str:
        """推断元素功能描述"""
        tag_name = self.tag_name.lower()

        # 基于标签类型的基础描述
        if tag_name == 'input':
            input_type = self.attributes.get('type', 'text').lower()
            if input_type in ['text', 'search']:
                # 基于placeholder或name推断具体功能
                placeholder = self.attributes.get('placeholder', '').lower()
                name = self.attributes.get('name', '').lower()
                if any(keyword in placeholder + name for keyword in ['search', '搜索', 'query', 'kw']):
                    return "搜索输入框"
                elif any(keyword in placeholder + name for keyword in ['login', '登录', 'username', 'user']):
                    return "登录输入框"
                elif any(keyword in placeholder + name for keyword in ['password', '密码', 'pwd']):
                    return "密码输入框"
                elif any(keyword in placeholder + name for keyword in ['email', '邮箱', 'mail']):
                    return "邮箱输入框"
                else:
                    return "文本输入框"
            elif input_type == 'password':
                return "密码输入框"
            elif input_type == 'email':
                return "邮箱输入框"
            elif input_type in ['submit', 'button']:
                return "提交按钮"
            elif input_type in ['checkbox', 'radio']:
                return f"{input_type}选择框"
            else:
                return f"{input_type}输入框"

        elif tag_name == 'button':
            text = self.text_content.lower()
            if any(keyword in text for keyword in ['submit', '提交', '确定', '登录', '注册']):
                return "提交按钮"
            elif any(keyword in text for keyword in ['search', '搜索']):
                return "搜索按钮"
            elif any(keyword in text for keyword in ['cancel', '取消', '关闭']):
                return "取消按钮"
            else:
                return "操作按钮"

        elif tag_name == 'a':
            text = self.text_content.lower()
            href = self.attributes.get('href', '').lower()
            if any(keyword in text + href for keyword in ['login', '登录']):
                return "登录链接"
            elif any(keyword in text + href for keyword in ['register', '注册', 'signup']):
                return "注册链接"
            elif any(keyword in text + href for keyword in ['home', '首页', 'index']):
                return "首页链接"
            elif href.startswith('mailto:'):
                return "邮件链接"
            elif href.startswith('tel:'):
                return "电话链接"
            elif any(keyword in text for keyword in ['nav', '导航', 'menu', '菜单']):
                return "导航链接"
            else:
                return "链接"

        elif tag_name == 'select':
            return "下拉选择框"
        elif tag_name == 'textarea':
            return "文本区域"
        elif tag_name == 'form':
            return "表单容器"
        elif tag_name in ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']:
            return f"{tag_name.upper()}标题"
        elif tag_name == 'img':
            alt = self.attributes.get('alt', '')
            return f"图片{f'({alt})' if alt else ''}"
        elif tag_name == 'video':
            return "视频元素"
        elif tag_name == 'audio':
            return "音频元素"
        elif tag_name == 'nav':
            return "导航容器"
        elif tag_name == 'div':
            # 基于class或id推断div功能
            class_name = self.attributes.get('class', '').lower()
            id_name = self.attributes.get('id', '').lower()
            combined = class_name + ' ' + id_name
            
            if any(keyword in combined for keyword in ['header', 'top', '头部']):
                return "页头容器"
            elif any(keyword in combined for keyword in ['footer', 'bottom', '页脚']):
                return "页脚容器"
            elif any(keyword in combined for keyword in ['nav', 'menu', '导航', '菜单']):
                return "导航容器"
            elif any(keyword in combined for keyword in ['content', 'main', '内容', '主体']):
                return "内容容器"
            elif any(keyword in combined for keyword in ['sidebar', 'aside', '侧边']):
                return "侧边容器"
            else:
                return "容器元素"
        else:
            return f"{tag_name}元素"

    def _format_key_attributes(self) -> str:
        """格式化关键属性"""
        key_attrs = []
        
        # ID属性
        if self.attributes.get('id'):
            key_attrs.append(f"id='{self.attributes['id']}'")
        
        # Class属性（只显示前3个类）
        if self.attributes.get('class'):
            classes = self.attributes['class'].split()[:3]
            key_attrs.append(f"class='{' '.join(classes)}'")
        
        # Name属性
        if self.attributes.get('name'):
            key_attrs.append(f"name='{self.attributes['name']}'")
        
        # Type属性（对input元素）
        if self.tag_name.lower() == 'input' and self.attributes.get('type'):
            key_attrs.append(f"type='{self.attributes['type']}'")
        
        # Href属性（对链接元素）
        if self.tag_name.lower() == 'a' and self.attributes.get('href'):
            href = self.attributes['href']
            # 截断长URL
            if len(href) > 50:
                href = href[:47] + "..."
            key_attrs.append(f"href='{href}'")
        
        return " | ".join(key_attrs)

    def _get_status_tags(self) -> str:
        """获取状态标签"""
        status_tags = []
        
        if not self.is_visible:
            status_tags.append("隐藏")
        if self.is_interactive:
            status_tags.append("可交互")
        if self.is_in_viewport:
            status_tags.append("视口内")
        if self.attributes.get('disabled'):
            status_tags.append("禁用")
        if self.attributes.get('readonly'):
            status_tags.append("只读")
        if self.attributes.get('required'):
            status_tags.append("必填")
        
        return " | ".join(status_tags)

    def _get_position_description(self) -> str:
        """获取位置描述"""
        xpath_parts = self.xpath.split('/')
        description_parts = []
        
        # 查找关键容器
        for i, part in enumerate(xpath_parts):
            if 'form' in part:
                description_parts.append("表单内")
            elif 'nav' in part:
                description_parts.append("导航区域")
            elif 'header' in part:
                description_parts.append("页头区域")
            elif 'footer' in part:
                description_parts.append("页脚区域")
            elif 'main' in part:
                description_parts.append("主内容区域")
            elif 'div[1]' in part and i > 2:  # 避免根级div
                description_parts.append("第1个容器")
            elif 'span[1]' in part:
                description_parts.append("第1个span")

        # 添加元素类型
        tag_name = self.tag_name.lower()
        if tag_name == 'input':
            description_parts.append("输入框")
        elif tag_name == 'button':
            description_parts.append("按钮")
        elif tag_name == 'a':
            description_parts.append("链接")

        return "".join(description_parts) if description_parts else "页面元素"

    def _get_special_attributes(self) -> str:
        """获取特殊属性信息（由子类重写）"""
        return ""

    def to_line(self) -> str:
        """统一的格式化方法"""
        parts = []

        # 1. 元素功能描述
        parts.append(self._get_element_function_description())

        # 2. 关键属性
        key_attrs = self._format_key_attributes()
        if key_attrs:
            parts.append(f"关键属性: {key_attrs}")

        # 3. 特殊信息（由子类提供）
        special_info = self._get_special_attributes()
        if special_info:
            parts.append(special_info)

        # 4. 状态标签
        status = self._get_status_tags()
        if status:
            parts.append(f"状态: {status}")

        # 5. 位置描述
        position = self._get_position_description()
        if position:
            parts.append(f"位置: {position}")

        # 6. XPath定位器（必须）
        parts.append(f"定位器: xpath='{self.xpath}'")

        return " | ".join(parts)

    def get_attribute(self, name: str, default: str = "") -> str:
        """安全获取属性值"""
        return str(self.attributes.get(name, default))


class LinkElement(DOMElementOutput):
    """链接元素专用模型"""
    href: str = Field(default="", description="链接地址")
    target: str = Field(default="", description="链接目标")
    title: str = Field(default="", description="链接标题")
    rel: str = Field(default="", description="链接关系")

    @classmethod
    def from_element_info(cls, elem) -> "LinkElement":
        """从ElementInfo创建LinkElement"""
        return cls(
            highlight_index=elem.highlight_index,
            text_content=elem.text_content or "",
            xpath=elem.xpath,
            tag_name=elem.tag_name,
            attributes=elem.attributes,
            is_visible=elem.is_visible,
            is_interactive=elem.is_interactive,
            is_in_viewport=getattr(elem, 'is_in_viewport', False),
            href=elem.attributes.get('href', ''),
            target=elem.attributes.get('target', ''),
            title=elem.attributes.get('title', ''),
            rel=elem.attributes.get('rel', '')
        )

    def _get_special_attributes(self) -> str:
        """获取链接特殊属性信息"""
        special_parts = []

        # 链接文本
        display_text = self.text_content
        if not display_text.strip():
            # 如果没有文本，尝试从title或href获取
            display_text = self.title or self.href

        # 截断长文本
        if len(display_text) > 50:
            display_text = display_text[:47] + "..."

        if display_text:
            special_parts.append(f"文本: '{display_text}'")

        # 链接地址（如果不是当前页面的锚点）
        if self.href and not self.href.startswith('#'):
            href_display = self.href
            if len(href_display) > 60:
                href_display = href_display[:57] + "..."
            special_parts.append(f"链接: {href_display}")

        # 打开方式
        if self.target:
            target_desc = {
                '_blank': '新窗口',
                '_self': '当前窗口',
                '_parent': '父窗口',
                '_top': '顶层窗口'
            }.get(self.target, self.target)
            special_parts.append(f"打开方式: {target_desc}")

        return " | ".join(special_parts)


class ButtonElement(DOMElementOutput):
    """按钮元素专用模型"""
    button_type: str = Field(default="", description="按钮类型")
    form_id: str = Field(default="", description="关联表单ID")
    disabled: bool = Field(default=False, description="是否禁用")

    @classmethod
    def from_element_info(cls, elem) -> "ButtonElement":
        """从ElementInfo创建ButtonElement"""
        return cls(
            highlight_index=elem.highlight_index,
            text_content=elem.text_content or "",
            xpath=elem.xpath,
            tag_name=elem.tag_name,
            attributes=elem.attributes,
            is_visible=elem.is_visible,
            is_interactive=elem.is_interactive,
            is_in_viewport=getattr(elem, 'is_in_viewport', False),
            button_type=elem.attributes.get('type', 'button'),
            form_id=elem.attributes.get('form', ''),
            disabled=elem.attributes.get('disabled') is not None
        )

    def _get_special_attributes(self) -> str:
        """获取按钮特殊属性信息"""
        special_parts = []

        # 按钮文本
        display_text = self.text_content.strip()
        if display_text:
            # 截断长文本
            if len(display_text) > 30:
                display_text = display_text[:27] + "..."
            special_parts.append(f"文本: '{display_text}'")

        # 按钮类型
        if self.button_type and self.button_type != 'button':
            type_desc = {
                'submit': '提交按钮',
                'reset': '重置按钮',
                'button': '普通按钮'
            }.get(self.button_type, self.button_type)
            special_parts.append(f"类型: {type_desc}")

        # 关联表单
        if self.form_id:
            special_parts.append(f"表单: {self.form_id}")

        return " | ".join(special_parts)


class InputElement(DOMElementOutput):
    """输入框元素专用模型"""
    input_type: str = Field(default="text", description="输入框类型")
    placeholder: str = Field(default="", description="占位符文本")
    value: str = Field(default="", description="当前值")
    name: str = Field(default="", description="字段名")
    required: bool = Field(default=False, description="是否必填")
    disabled: bool = Field(default=False, description="是否禁用")
    readonly: bool = Field(default=False, description="是否只读")
    maxlength: Optional[int] = Field(default=None, description="最大长度")

    @classmethod
    def from_element_info(cls, elem) -> "InputElement":
        """从ElementInfo创建InputElement"""
        maxlength = elem.attributes.get('maxlength')
        return cls(
            highlight_index=elem.highlight_index,
            text_content=elem.text_content or "",
            xpath=elem.xpath,
            tag_name=elem.tag_name,
            attributes=elem.attributes,
            is_visible=elem.is_visible,
            is_interactive=elem.is_interactive,
            is_in_viewport=getattr(elem, 'is_in_viewport', False),
            input_type=elem.attributes.get('type', 'text'),
            placeholder=elem.attributes.get('placeholder', ''),
            value=elem.attributes.get('value', ''),
            name=elem.attributes.get('name', ''),
            required=elem.attributes.get('required') is not None,
            disabled=elem.attributes.get('disabled') is not None,
            readonly=elem.attributes.get('readonly') is not None,
            maxlength=int(maxlength) if maxlength and maxlength.isdigit() else None
        )

    def _get_special_attributes(self) -> str:
        """获取输入框特殊属性信息"""
        special_parts = []

        # 输入类型
        if self.input_type:
            special_parts.append(f"类型: {self.input_type}")

        # 字段名
        if self.name:
            special_parts.append(f"字段名: {self.name}")

        # 提示文本
        if self.placeholder:
            # 截断长提示文本
            placeholder_text = self.placeholder[:50] + "..." if len(self.placeholder) > 50 else self.placeholder
            special_parts.append(f"提示文本: '{placeholder_text}'")

        # 当前值
        if self.value:
            value_text = self.value[:30] + "..." if len(self.value) > 30 else self.value
            special_parts.append(f"当前值: '{value_text}'")

        # 长度限制
        if self.maxlength:
            special_parts.append(f"最大长度: {self.maxlength}")

        return " | ".join(special_parts)


class ActionableElement(DOMElementOutput):
    """可操作元素专用模型"""
    element_type: str = Field(description="元素类型")
    role: str = Field(default="", description="ARIA角色")
    tabindex: Optional[int] = Field(default=None, description="Tab索引")

    @classmethod
    def from_element_info(cls, elem) -> "ActionableElement":
        """从ElementInfo创建ActionableElement"""
        # 确定元素类型
        element_type = elem.tag_name.lower()
        if elem.attributes.get('role'):
            element_type = f"{element_type}[role={elem.attributes['role']}]"
        elif elem.tag_name.lower() == 'input':
            input_type = elem.attributes.get('type', 'text')
            element_type = f"input[type={input_type}]"

        tabindex = elem.attributes.get('tabindex')
        return cls(
            highlight_index=elem.highlight_index,
            text_content=elem.text_content or "",
            xpath=elem.xpath,
            tag_name=elem.tag_name,
            attributes=elem.attributes,
            is_visible=elem.is_visible,
            is_interactive=elem.is_interactive,
            is_in_viewport=getattr(elem, 'is_in_viewport', False),
            element_type=element_type,
            role=elem.attributes.get('role', ''),
            tabindex=int(tabindex) if tabindex and tabindex.lstrip('-').isdigit() else None
        )

    def to_line(self) -> str:
        """转换为可操作元素专用的单行字符串格式"""
        # Truncate long text content to keep output readable
        display_text = self.text_content[:50] + "..." if len(self.text_content) > 50 else self.text_content

        return f"[{self.highlight_index}] {self.element_type}: '{display_text}' xpath='{self.xpath}'"
