"""
Delegation tools for CrawlerAgent to delegate tasks to specialized agents.

This module provides LangChain tools that allow CrawlerAgent to intelligently
delegate tasks to BrowserAgent and ElementAgent when needed.
"""

import logging
from langchain_core.tools import tool

logger = logging.getLogger(__name__)








@tool
def smart_element_finder(element_description: str) -> str:
    """
    Smart element finder with precision-first strategy and screenshot fallback.

    Uses a layered approach to find elements with maximum precision:
    1. First tries the most specific tool for the element type
    2. Falls back to small-scope specialized tools if needed
    3. Only uses broad-scope tools as last resort
    4. If DOM tools fail to find the target, uses screenshot analysis as fallback

    Principle: Precise selection > Small-scope selection > Avoid broad-scope selection > Screenshot analysis

    Args:
        element_description: Natural language description of the element to find

    Returns:
        Information about found elements with xpath selectors for precise targeting,
        or screenshot-based analysis if DOM tools fail

    Example:
        smart_element_finder("搜索框")
        smart_element_finder("提交按钮")
        smart_element_finder("登录表单")
        smart_element_finder("获取页面主要文字内容")
    """
    try:
        from ..agents.element_agent import build_element_agent

        logger.info(f"Smart element search (precision-first with screenshot fallback): {element_description}")

        # 强制精确优先的任务指令
        task = f"""
        查找元素: "{element_description}"

        🚨 强制工具选择流程 - 严格按照以下步骤执行:

        📊 工具选择决策矩阵:
        ┌─────────────────┬─────────────────────────┬─────────────────────────┐
        │ 元素类型        │ 首选工具                │ 何时不使用              │
        ├─────────────────┼─────────────────────────┼─────────────────────────┤
        │ 按钮            │ dom_get_buttons_enhanced│ 如果查找链接            │
        │ 输入框          │ dom_get_inputs_enhanced │ 如果查找按钮            │
        │ 链接            │ dom_get_links_enhanced  │ 如果查找按钮            │
        │ 表单            │ dom_get_form_elements   │ 如果查找媒体            │
        │ 导航            │ dom_get_navigation_elements│ 如果查找内容         │
        │ 媒体            │ dom_get_media_elements  │ 如果查找文本            │
        │ 内容/文本       │ dom_get_content_elements│ 仅当其他工具失败时      │
        └─────────────────┴─────────────────────────┴─────────────────────────┘

        🚫 避免的反模式:
        - 不要对按钮、输入框或链接使用 dom_get_content_elements
        - 不要在有特定工具时使用通用工具
        - 不要跳过参数优化
        - 不要忽略完整性评估结果

        第一步: 分析元素类型
        从描述中识别最具体的元素类型:
        - "按钮", "button", "提交", "确认", "点击" → 按钮类型
        - "输入", "搜索框", "文本框", "input", "search" → 输入框类型
        - "链接", "link", "更多", "查看", "跳转" → 链接类型
        - "表单", "form", "登录", "注册" → 表单类型
        - "导航", "菜单", "nav", "menu" → 导航类型
        - "图片", "视频", "媒体", "image", "video" → 媒体类型
        - "文字", "内容", "文本", "text", "content" → 内容类型

        🔧 动态参数生成方法:
        对于内容类型，使用以下方法提取关键词:
        1. 移除常用词: "的", "了", "在", "是", "有", "和", "the", "a", "an", "in", "on", "at"
        2. 保留2-3个最具体的名词/形容词
        3. 示例:
           - "找到产品的详细信息" → "产品 详细信息"
           - "查看用户评论内容" → "用户 评论"
           - "find product details" → "product details"

        第二步: 使用优化参数的特定工具 (强制执行)
        根据元素类型，必须首先尝试特定工具并使用最优参数:

        - 按钮类型 → dom_get_buttons_enhanced(
            include_disabled=False,  # 跳过禁用按钮，除非特别需要
            include_hidden=False,    # 跳过隐藏按钮，除非特别需要
            require_text=True        # 要求有文本以便更好识别
          ) 优先使用

        - 输入框类型 → dom_get_inputs_enhanced(
            input_types=["text", "search", "email", "password"],  # 用于搜索/表单上下文
            include_disabled=False,  # 跳过禁用输入框，除非特别需要
            include_readonly=True    # 包含只读输入框以确保完整性
          ) 优先使用

        - 链接类型 → dom_get_links_enhanced(
            require_href=True,       # 要求有href属性以确保可操作
            include_anchors=False,   # 跳过锚点，除非特别需要
            external_only=False      # 包含所有链接，除非特别指定
          ) 优先使用

        - 表单类型 → dom_get_form_elements() 优先使用 (无参数)

        - 导航类型 → dom_get_navigation_elements() 优先使用 (无参数)

        - 媒体类型 → dom_get_media_elements() 优先使用 (无参数)

        - 内容类型 → dom_get_content_elements(
            query_text="<从元素描述中提取2-3个关键词>",  # 使用具体关键词
            max_results=20,          # 标准限制
            similarity_threshold=0.3  # 更高阈值以确保精确性
          ) 优先使用

        第三步: 评估结果和完整性检查
        对于每个工具结果 (强制执行):
        A) 统计找到的元素数量: count = len(results)
        B) 调用 dom_assess_information_completeness(user_query="{element_description}", current_results_count=count)
        C) 分析完整性评估响应:
           - 如果包含 "COMPLETE" 或 "sufficient" → ✅ 完整: 返回带xpath选择器的结果
           - 如果包含 "INCOMPLETE" 或 "more elements" → ❌ 不完整: 检查建议并进入第四步
           - 如果 count == 0 → 🔍 无元素: 直接进入第四步

        📝 完整性检查示例:
        - 找到3个按钮 → dom_assess_information_completeness("找到登录按钮", 3)
        - 评估结果显示 "COMPLETE" → 返回这3个按钮
        - 评估结果显示 "INCOMPLETE, may have more buttons" → 进入第四步A

        第四步: 完整性驱动的回退策略
        如果第三步显示不完整，按以下升级策略执行:

        4A) 范围扩展 (如果评估建议存在更多元素):
        - 按钮搜索 → dom_get_clickable_elements(exclude_links=True)  # 专注于非链接的可点击元素
        - 输入框搜索 → dom_get_form_elements()  # 获取所有表单上下文
        - 链接搜索 → dom_get_navigation_elements()  # 获取导航上下文
        - 任何特定搜索 → dom_get_interactive_elements_smart()  # 全面的交互元素扫描

        4B) 导航检测 (如果评估建议有分页/更多链接):
        - 查找分页: dom_get_links_enhanced(
            require_href=True,
            include_anchors=False,
            external_only=False
          ) 使用关键词 "更多", "下一页", "查看全部", "next", "more"
        - 查找导航: dom_get_navigation_elements()
        - 向用户报告导航选项及具体xpath选择器

        4C) 全面扫描 (最后手段):
        - 使用 dom_get_content_elements(
            query_text="<使用描述中更广泛的关键词>",
            max_results=50,           # 增加限制以进行全面搜索
            similarity_threshold=0.1  # 降低阈值以获得更广泛的结果
          )
        - 对合并结果重新运行完整性评估

        第五步: 截图回退
        仅当所有DOM工具都无法找到任何元素时，使用浏览器代理截图分析。

        🚫 禁止操作:
        - 不要跳过任何工具结果的完整性评估
        - 除非元素类型是内容类型，否则不要将 dom_get_content_elements() 作为首选

        📋 参数优化指南:
        - 对于按钮: require_text=True (更好的识别)
        - 对于输入框: include_disabled=False (专注于可操作的)
        - 对于链接: require_href=True (确保可操作的链接)
        - 对于内容: similarity_threshold=0.3 (精确优先), 0.1 (召回回退)
        - 对于内容: max_results=20 (初始), 50 (全面扫描)

        💡 执行示例:

        示例1: "找到登录按钮"
        ✅ 正确: dom_get_buttons_enhanced(include_disabled=False, include_hidden=False, require_text=True)
        ❌ 错误: dom_get_content_elements(query_text="登录按钮")

        示例2: "找到搜索框"
        ✅ 正确: dom_get_inputs_enhanced(input_types=["text", "search"], include_disabled=False, include_readonly=True)
        ❌ 错误: dom_get_content_elements(query_text="搜索框")

        示例3: "找到更多链接"
        ✅ 正确: dom_get_links_enhanced(require_href=True, include_anchors=False, external_only=False)
        ❌ 错误: dom_get_content_elements(query_text="更多")

        示例4: "找到产品信息文本"
        ✅ 正确: dom_get_content_elements(query_text="产品 信息", max_results=20, similarity_threshold=0.3)
        ✅ 也可以: 这是唯一应该首先使用 content_elements 的情况

        ⚠️ 语言匹配: 查询关键词必须与页面语言匹配。

        返回格式:
        1. 工具选择推理: "使用了 [工具名] 因为元素类型是 [类型]"
        2. 完整性评估: "找到 [X] 个元素，完整性: [完整/不完整]"
        3. 带精确xpath选择器的结果
        4. 如果不完整: "评估建议: [导航选项/需要扩大范围]"
        5. 如果使用回退: "由于 [不完整/无结果] 升级到 [回退工具]"

        📋 完整执行示例:
        用户: "找到登录按钮"

        第一步: 分析 → "登录" + "按钮" → 按钮类型
        第二步: 调用 dom_get_buttons_enhanced(include_disabled=False, include_hidden=False, require_text=True)
        第三步: 找到2个按钮 → dom_assess_information_completeness("找到登录按钮", 2)
        评估: "COMPLETE - 找到足够的登录按钮"

        响应:
        "使用了 dom_get_buttons_enhanced 因为元素类型是按钮类型
        找到 2 个元素，完整性: 完整
        结果: [xpath1='//*[@id="login-btn"]', xpath2='//*[@class="signin-button"]']"
        """

        element_agent = build_element_agent()
        result = element_agent.invoke(task)

        output = result.get('output', str(result))
        logger.info(f"Smart element search (precision-first with screenshot fallback) completed")
        return output

    except Exception as e:
        error_msg = f"Smart element search failed: {str(e)}"
        logger.error(error_msg)
        return f"❌ {error_msg}"


# quick_page_scan removed - use smart_element_finder with dom_get_interactive_elements_smart instead


@tool
def smart_browser_action_finder(action_description: str) -> str:
    """
    智能浏览器操作查找器，职责分离策略。

    根据操作描述智能选择最合适的浏览器工具执行操作。
    专注于浏览器操作，对于需要元素查找的操作会指导用户先使用DOM工具。

    Args:
        action_description: 浏览器操作的描述，如"点击按钮"、"截图保存"、"输入文本"等

    Returns:
        执行结果或操作指导信息

    Example:
        smart_browser_action_finder("截图保存")  # 直接执行
        smart_browser_action_finder("点击登录按钮")  # 返回指导信息
        smart_browser_action_finder("导航到google.com")  # 直接执行
    """
    try:
        from ..agents.browser_agent import build_browser_agent

        logger.info(f"Smart browser action search (DOM-first strategy): {action_description}")

        task = f"""
        执行浏览器操作: "{action_description}"

        📋 直接操作:
        - 导航/URL → navigate_browser
        - 截图 → take_screenshot
        - 分析页面 → analyze_screenshot
        - 页面信息 → get_page_info
        - 键盘操作 → browser_press_key

        🎯 元素操作:
        - 如果提供了xpath → 直接执行 (click_element, type_text, hover_element)
        - 如果只有描述 → 返回指导:
          "⚠️ 需要先获取xpath选择器。使用 smart_element_finder('元素描述') 找到元素，然后提供xpath进行直接操作。"

        ⚠️ 原则: BrowserAgent处理浏览器操作，不负责元素查找。

        返回: 操作结果或元素查找指导。
        """

        browser_agent = build_browser_agent()
        result = browser_agent.invoke(task)

        output = result.get('output', str(result))
        logger.info(f"Smart browser action search (DOM-first strategy) completed")
        return output

    except Exception as e:
        error_msg = f"Smart browser action search failed: {str(e)}"
        logger.error(error_msg)
        return f"❌ {error_msg}"


# Transfer functions removed - use smart delegation functions instead to avoid circular delegation and maintain clean supervisor pattern


# 委托工具列表 - 使用纯委托模式，避免循环移交
DELEGATION_TOOLS = [
    smart_element_finder,             # 智能元素查找
    smart_browser_action_finder,      # 智能浏览器操作查找
]


def get_delegation_tools():
    """
    Get delegation tools for CrawlerAgent.

    Returns:
        List of delegation tools that can be added to CrawlerAgent's tool list
    """
    return DELEGATION_TOOLS.copy()
