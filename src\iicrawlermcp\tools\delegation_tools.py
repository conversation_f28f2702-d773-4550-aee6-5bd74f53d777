"""
Delegation tools for CrawlerAgent to delegate tasks to specialized agents.

This module provides LangChain tools that allow CrawlerAgent to intelligently
delegate tasks to BrowserAgent and ElementAgent when needed.
"""

import logging
from langchain_core.tools import tool

logger = logging.getLogger(__name__)








@tool
def smart_element_finder(element_description: str) -> str:
    """
    Smart element finder with precision-first strategy and screenshot fallback.

    Uses a layered approach to find elements with maximum precision:
    1. First tries the most specific tool for the element type
    2. Falls back to small-scope specialized tools if needed
    3. Only uses broad-scope tools as last resort
    4. If DOM tools fail to find the target, uses screenshot analysis as fallback

    Principle: Precise selection > Small-scope selection > Avoid broad-scope selection > Screenshot analysis

    Args:
        element_description: Natural language description of the element to find

    Returns:
        Information about found elements with xpath selectors for precise targeting,
        or screenshot-based analysis if DOM tools fail

    Example:
        smart_element_finder("搜索框")
        smart_element_finder("提交按钮")
        smart_element_finder("登录表单")
        smart_element_finder("获取页面主要文字内容")
    """
    try:
        from ..agents.element_agent import build_element_agent

        logger.info(f"Smart element search (precision-first with screenshot fallback): {element_description}")

        # 强制精确优先的任务指令
        task = f"""
        查找元素: "{element_description}"
        """

        element_agent = build_element_agent()
        result = element_agent.invoke(task)

        output = result.get('output', str(result))
        logger.info(f"Smart element search (precision-first with screenshot fallback) completed")
        return output

    except Exception as e:
        error_msg = f"Smart element search failed: {str(e)}"
        logger.error(error_msg)
        return f"❌ {error_msg}"


# quick_page_scan removed - use smart_element_finder with dom_get_interactive_elements_smart instead


@tool
def smart_browser_action_finder(action_description: str) -> str:
    """
    智能浏览器操作查找器，职责分离策略。

    根据操作描述智能选择最合适的浏览器工具执行操作。
    专注于浏览器操作，对于需要元素查找的操作会指导用户先使用DOM工具。

    Args:
        action_description: 浏览器操作的描述，如"点击按钮"、"截图保存"、"输入文本"等

    Returns:
        执行结果或操作指导信息

    Example:
        smart_browser_action_finder("截图保存")  # 直接执行
        smart_browser_action_finder("点击登录按钮")  # 返回指导信息
        smart_browser_action_finder("导航到google.com")  # 直接执行
    """
    try:
        from ..agents.browser_agent import build_browser_agent

        logger.info(f"Smart browser action search (DOM-first strategy): {action_description}")

        task = f"""
        执行浏览器操作: "{action_description}"

        🚨 强制操作分类流程 - 严格按照以下步骤执行:

        📊 浏览器操作决策矩阵:
        ┌─────────────────┬─────────────────────────┬─────────────────────────┐
        │ 操作类型        │ 使用工具                │ 参数要求                │
        ├─────────────────┼─────────────────────────┼─────────────────────────┤
        │ 页面导航        │ navigate_browser_advanced│ url参数                 │
        │ 截图保存        │ take_screenshot_advanced │ filename, full_page     │
        │ 截图分析        │ analyze_screenshot      │ task_description        │
        │ 页面快照        │ browser_snapshot        │ 无参数                  │
        │ 页面信息        │ get_page_info           │ 无参数                  │
        │ 等待操作        │ browser_wait_for        │ time/text/text_gone     │
        │ JavaScript执行  │ browser_evaluate        │ function, element, ref  │
        │ 键盘按键        │ browser_press_key       │ key参数                 │
        │ 下拉选择        │ browser_select_option   │ element, ref, values    │
        └─────────────────┴─────────────────────────┴─────────────────────────┘

        第一步: 分析操作类型
        从描述中识别具体的操作类型:
        - "导航", "打开", "访问", "跳转", "navigate" → 页面导航类型
        - "截图", "保存图片", "screenshot", "capture" → 截图保存类型
        - "分析截图", "识别元素", "analyze" → 截图分析类型
        - "快照", "页面结构", "snapshot", "accessibility" → 页面快照类型
        - "页面信息", "标题", "URL", "page info" → 页面信息类型
        - "等待", "延迟", "wait", "sleep" → 等待操作类型
        - "执行JS", "JavaScript", "evaluate", "脚本" → JavaScript执行类型
        - "按键", "键盘", "press key", "输入键" → 键盘按键类型
        - "选择选项", "下拉框", "select option" → 下拉选择类型
        - "点击", "输入", "悬停", "click", "type", "hover" → 元素操作类型

        第二步: 工具选择和参数优化
        根据操作类型选择最合适的工具:

        - 页面导航类型 → navigate_browser_advanced(url="<提取URL>")
        - 截图保存类型 → take_screenshot_advanced(
            filename="<生成描述性文件名>",
            full_page=True  # 默认全页截图
          )
        - 截图分析类型 → analyze_screenshot(
            task_description="<具体分析任务>"
          )
        - 页面快照类型 → browser_snapshot() # 无参数
        - 页面信息类型 → get_page_info() # 无参数
        - 等待操作类型 → browser_wait_for(
            time=<秒数>,  # 如果指定时间
            text="<等待文本>",  # 如果等待文本出现
            text_gone="<消失文本>"  # 如果等待文本消失
          )
        - JavaScript执行类型 → browser_evaluate(
            function="<JavaScript代码>",
            element="<元素描述>",  # 可选
            ref="<xpath选择器>"  # 可选
          )
        - 键盘按键类型 → browser_press_key(key="<按键名>")
        - 下拉选择类型 → browser_select_option(
            element="<元素描述>",
            ref="<xpath选择器>",
            values=["<选项值>"]
          )

        第三步: 元素操作特殊处理
        对于元素操作类型 (点击、输入、悬停):
        - 如果提供了xpath选择器 → 直接执行对应工具:
          * click_element_advanced(element="描述", ref="xpath", double_click=False)
          * type_text_advanced(element="描述", ref="xpath", text="文本", submit=False)
          * browser_hover(element="描述", ref="xpath")
        - 如果只有元素描述 → 返回指导信息:
          "⚠️ 需要先获取xpath选择器。使用 smart_element_finder('元素描述') 找到元素，然后提供xpath进行直接操作。"

        🚫 避免的反模式:
        - 不要对需要xpath的操作跳过元素查找步骤
        - 不要混淆截图保存和截图分析功能
        - 不要在JavaScript执行时忽略XPath转义
        - 不要对简单操作使用复杂工具

        📋 参数优化指南:
        - 截图文件名: 使用描述性名称，如"login_page.png", "search_results.png"
        - 等待时间: 一般1-5秒，复杂页面可用10秒
        - JavaScript函数: 使用箭头函数格式 "() => {{ code }}" 或 "(element) => {{ code }}"
        - 按键名称: 使用标准名称如"Enter", "ArrowDown", "Escape", "Tab"

        💡 执行示例:

        示例1: "导航到百度首页"
        ✅ 正确: navigate_browser_advanced(url="https://www.baidu.com")
        ❌ 错误: 使用截图或其他工具

        示例2: "截图保存当前页面"
        ✅ 正确: take_screenshot_advanced(filename="current_page.png", full_page=True)
        ❌ 错误: analyze_screenshot(task_description="保存页面")

        示例3: "分析页面中的登录元素"
        ✅ 正确: analyze_screenshot(task_description="找到页面上的登录相关元素，包括用户名输入框、密码输入框和登录按钮")
        ❌ 错误: take_screenshot_advanced(filename="login_analysis.png")

        示例4: "等待3秒"
        ✅ 正确: browser_wait_for(time=3)
        ❌ 错误: browser_press_key(key="wait")

        示例5: "按回车键"
        ✅ 正确: browser_press_key(key="Enter")
        ❌ 错误: browser_evaluate(function="() => {{ document.dispatchEvent(new KeyboardEvent('keydown', {{key: 'Enter'}})); }}")

        示例6: "点击登录按钮" (无xpath)
        ✅ 正确: "⚠️ 需要先获取xpath选择器。使用 smart_element_finder('登录按钮') 找到元素，然后提供xpath进行直接操作。"
        ❌ 错误: 直接尝试点击操作

        示例7: "点击登录按钮" (有xpath)
        ✅ 正确: click_element_advanced(element="登录按钮", ref="//button[@id='login-btn']", double_click=False)
        ❌ 错误: browser_evaluate(function="(element) => element.click()", element="登录按钮", ref="//button[@id='login-btn']")

        ⚠️ 职责分离原则:
        - BrowserAgent专注浏览器级别操作
        - 元素查找委托给smart_element_finder
        - 复杂分析委托给analyze_screenshot
        - 保持工具职责清晰，避免功能重叠

        返回格式:
        1. 操作分类: "识别为 [操作类型]"
        2. 工具选择: "使用 [工具名] 执行操作"
        3. 参数说明: "参数: [具体参数值]"
        4. 执行结果: [工具返回的结果]
        5. 如果需要元素查找: "需要先使用 smart_element_finder 获取xpath选择器"
        """

        browser_agent = build_browser_agent()
        result = browser_agent.invoke(task)

        output = result.get('output', str(result))
        logger.info(f"Smart browser action search (DOM-first strategy) completed")
        return output

    except Exception as e:
        error_msg = f"Smart browser action search failed: {str(e)}"
        logger.error(error_msg)
        return f"❌ {error_msg}"


# Transfer functions removed - use smart delegation functions instead to avoid circular delegation and maintain clean supervisor pattern


# 委托工具列表 - 使用纯委托模式，避免循环移交
DELEGATION_TOOLS = [
    smart_element_finder,             # 智能元素查找
    smart_browser_action_finder,      # 智能浏览器操作查找
]


def get_delegation_tools():
    """
    Get delegation tools for CrawlerAgent.

    Returns:
        List of delegation tools that can be added to CrawlerAgent's tool list
    """
    return DELEGATION_TOOLS.copy()
